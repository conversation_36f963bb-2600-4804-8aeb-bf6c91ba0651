package model

import (
	"time"
)

type Order struct {
	ID                 uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	CreatedAt          time.Time `gorm:"column:created_at;not null" json:"created_at"`
	OrderAmount        float64   `gorm:"column:order_amount;type:double precision;not null" json:"order_amount"`
	AppliedVoucherID   *uint64   `gorm:"column:applied_voucher_id;index" json:"applied_voucher_id"`
	CalculationStatus  string    `gorm:"column:calculation_status;type:varchar(50);not null;default:'SUCCESS'" json:"calculation_status"`
	CalculationMessage string    `gorm:"column:calculation_message;type:text" json:"calculation_message"`
	UserID             uint64    `gorm:"column:user_id;not null;index" json:"user_id"`

	Items []OrderItem `gorm:"foreignKey:OrderID;constraint:OnDelete:CASCADE" json:"items,omitempty"`
}

func (Order) TableName() string { return "orders" }

type OrderItem struct {
	ID        uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time `gorm:"column:created_at;not null" json:"created_at"`
	OrderID   uint64    `gorm:"column:order_id;index" json:"order_id"`
	ProductID uint64    `gorm:"column:product_id;index" json:"product_id"`
	Quantity  uint64    `gorm:"column:quantity;not null" json:"quantity"`
}

func (OrderItem) TableName() string { return "order_items" }

type CreateOrderRequest struct {
	UserID         uint64      `json:"user_id" validate:"required"`
	OrderAmount    float64     `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp time.Time   `json:"order_timestamp" validate:"required"`
	VoucherCode    *string     `json:"voucher_code"`
	Items          []OrderItem `json:"items" validate:"required,min=1"`
}

type CreateOrderResponse struct {
	OrderAmount        float64 `json:"order_amount"`
	AppliedVoucherCode *string `json:"applied_voucher_code"`
	DiscountAmount     float64 `json:"discount_amount"`
	FinalAmount        float64 `json:"final_amount"`
	Status             string  `json:"status"`
	Message            string  `json:"message"`
}

type VoucherEligibilityResponse struct {
	Eligible         bool    `json:"eligible"`
	VoucherID        uint64  `json:"voucher_id"`
	VoucherCode      string  `json:"voucher_code"`
	DiscountAmount   float64 `json:"discount_amount"`
	DiscountTypeName string  `json:"discount_type_name"`
	DiscountTypeCode string  `json:"discount_type_code"`
	Message          string  `json:"message"`
	Title            string  `json:"title"`
	Description      string  `json:"description"`
	Status           *string `json:"status,omitempty"`
}

type ListOrdersRequest struct {
	UserID *uint64 `json:"user_id"`
	Page   int32   `json:"page" validate:"min=1"`
	Limit  int32   `json:"limit" validate:"min=1,max=100"`
	Search *string `json:"search"`
}

type ListOrdersByVoucherRequest struct {
	VoucherID uint64 `json:"voucher_id" validate:"required"`
	Page      int32  `json:"page" validate:"min=1"`
	Limit     int32  `json:"limit" validate:"min=1,max=100"`
}

type GetUserOrderCountRequest struct {
	UserID uint64 `json:"user_id" validate:"required"`
}

type GetUserVoucherUsageCountRequest struct {
	UserID    uint64 `json:"user_id" validate:"required"`
	VoucherID uint64 `json:"voucher_id" validate:"required"`
}

type OrderWithPagination struct {
	Data  []Order `json:"data"`
	Total int32   `json:"total"`
	Page  int32   `json:"page"`
	Limit int32   `json:"limit"`
}

func NewOrder(userID uint64, orderAmount float64, voucherID *uint64, calculationStatus, calculationMessage string) *Order {
	return &Order{
		UserID:             userID,
		OrderAmount:        orderAmount,
		AppliedVoucherID:   voucherID,
		CalculationStatus:  calculationStatus,
		CalculationMessage: calculationMessage,
		CreatedAt:          time.Now(),
	}
}

func NewOrderItem(orderID, productID uint64, quantity uint64) *OrderItem {
	return &OrderItem{
		OrderID:   orderID,
		ProductID: productID,
		Quantity:  quantity,
		CreatedAt: time.Now(),
	}
}
