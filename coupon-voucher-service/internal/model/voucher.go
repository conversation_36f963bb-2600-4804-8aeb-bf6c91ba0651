package model

import (
	"database/sql/driver"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// Int32Array is a custom type for handling PostgreSQL integer arrays
type Int32Array []int32

// <PERSON><PERSON> implements the sql.Scanner interface for reading from database
func (a *Int32Array) Scan(value any) error {
	if value == nil {
		*a = nil
		return nil
	}

	switch v := value.(type) {
	case string:
		// Handle string representation like "{1,2,3}"
		if v == "" || v == "{}" {
			*a = []int32{}
			return nil
		}

		// Remove braces and split by comma
		v = strings.Trim(v, "{}")
		if v == "" {
			*a = []int32{}
			return nil
		}

		parts := strings.Split(v, ",")
		result := make([]int32, len(parts))

		for i, part := range parts {
			num, err := strconv.ParseInt(strings.TrimSpace(part), 10, 32)
			if err != nil {
				return fmt.Erro<PERSON>("failed to parse int32 from '%s': %w", part, err)
			}
			result[i] = int32(num)
		}

		*a = result
		return nil
	default:
		return fmt.Errorf("cannot scan %T into Int32Array", value)
	}
}

// Value implements the driver.Valuer interface for writing to database
func (a Int32Array) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}

	if len(a) == 0 {
		return "{}", nil
	}

	parts := make([]string, len(a))
	for i, v := range a {
		parts[i] = strconv.FormatInt(int64(v), 10)
	}

	return "{" + strings.Join(parts, ",") + "}", nil
}

// TimeArray handles PostgreSQL date arrays
type TimeArray []time.Time

// Scan implements sql.Scanner interface for date arrays
func (a *TimeArray) Scan(value any) error {
	if value == nil {
		*a = nil
		return nil
	}

	switch v := value.(type) {
	case string:
		// PostgreSQL returns date arrays as strings like "{2025-08-15,2025-11-28}"
		if v == "" || v == "{}" {
			*a = []time.Time{}
			return nil
		}

		// Remove braces and split by comma
		v = strings.Trim(v, "{}")
		if v == "" {
			*a = []time.Time{}
			return nil
		}

		parts := strings.Split(v, ",")
		result := make([]time.Time, len(parts))

		for i, part := range parts {
			// Parse date string (format: 2025-08-15)
			t, err := time.Parse("2006-01-02", strings.TrimSpace(part))
			if err != nil {
				return fmt.Errorf("failed to parse date from '%s': %w", part, err)
			}
			result[i] = t
		}

		*a = result
		return nil
	default:
		return fmt.Errorf("cannot scan %T into TimeArray", value)
	}
}

// Value implements driver.Valuer interface for date arrays
func (a TimeArray) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}

	if len(a) == 0 {
		return "{}", nil
	}

	parts := make([]string, len(a))
	for i, v := range a {
		parts[i] = v.Format("2006-01-02")
	}

	return "{" + strings.Join(parts, ",") + "}", nil
}

type UsageMethod string

const (
	UsageMethodManual    UsageMethod = "MANUAL"
	UsageMethodAutomatic UsageMethod = "AUTO"
)

type VoucherStatus string

const (
	VoucherStatusActive   VoucherStatus = "ACTIVE"
	VoucherStatusInactive VoucherStatus = "INACTIVE"
	VoucherStatusExpired  VoucherStatus = "EXPIRED"
)

type TimeRestrictionType string

const (
	TimeRestrictionTypeDaysOfWeek     TimeRestrictionType = "DAYS_OF_WEEK"
	TimeRestrictionTypeHoursOfDay     TimeRestrictionType = "HOURS_OF_DAY"
	TimeRestrictionTypeSpecificDates  TimeRestrictionType = "SPECIFIC_DATES"
	TimeRestrictionTypeRecurringDates TimeRestrictionType = "RECURRING_DATES"
)

type RecurrencePattern string

const (
	RecurrencePatternDaily     RecurrencePattern = "DAILY"
	RecurrencePatternWeekly    RecurrencePattern = "WEEKLY"
	RecurrencePatternMonthly   RecurrencePattern = "MONTHLY"
	RecurrencePatternQuarterly RecurrencePattern = "QUARTERLY"
	RecurrencePatternYearly    RecurrencePattern = "YEARLY"
)

type DiscountType struct {
	ID          uint64    `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	CreatedAt   time.Time `gorm:"column:created_at" json:"created_at"`
	TypeCode    string    `gorm:"type:varchar(50);not null;unique;column:type_code" json:"type_code"`
	TypeName    string    `gorm:"type:varchar(255);not null;column:type_name" json:"type_name"`
	Description string    `gorm:"column:description" json:"description"`
	IsActive    bool      `gorm:"not null;default:true;column:is_active" json:"is_active"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updated_at"`
}

type Voucher struct {
	ID                uint64        `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	CreatedAt         time.Time     `gorm:"column:created_at" json:"created_at"`
	VoucherCode       string        `gorm:"type:varchar(255);not null;unique;column:voucher_code" json:"voucher_code"`
	Title             string        `gorm:"type:varchar(255);not null;column:title" json:"title"`
	Description       string        `gorm:"column:description" json:"description"`
	DiscountTypeID    uint64        `gorm:"not null;column:discount_type_id" json:"discount_type_id"`
	DiscountValue     float64       `gorm:"not null;column:discount_value" json:"discount_value"`
	UsageMethod       UsageMethod   `gorm:"type:varchar(50);not null;column:usage_method" json:"usage_method"`
	ValidFrom         time.Time     `gorm:"not null;column:valid_from" json:"valid_from"`
	ValidUntil        time.Time     `gorm:"not null;column:valid_until" json:"valid_until"`
	MaxUsageCount     *int          `gorm:"column:max_usage_count" json:"max_usage_count"`
	CurrentUsageCount int           `gorm:"not null;default:0;column:current_usage_count" json:"current_usage_count"`
	MinOrderAmount    float64       `gorm:"not null;default:0;column:min_order_amount" json:"min_order_amount"`
	MaxDiscountAmount *float64      `gorm:"column:max_discount_amount" json:"max_discount_amount"`
	CreatedBy         uint64        `gorm:"not null;column:created_by" json:"created_by"`
	UpdatedAt         time.Time     `gorm:"column:updated_at" json:"updated_at"`
	MaxUsagePerUser   *int          `gorm:"column:max_usage_per_user" json:"max_usage_per_user"`
	Status            VoucherStatus `gorm:"type:varchar(50);not null;default:'ACTIVE';column:status" json:"status"`

	DiscountType         *DiscountType                `gorm:"-" json:"discount_type,omitempty"`
	ProductRestrictions  []*VoucherProductRestriction `gorm:"-" json:"product_restrictions,omitempty"`
	TimeRestrictions     []*VoucherTimeRestriction    `gorm:"-" json:"time_restrictions,omitempty"`
	UserEligibilityRules []*VoucherUserEligibility    `gorm:"-" json:"user_eligibility,omitempty"`
	UserUsage            []*UserVoucherUsage          `gorm:"-" json:"user_usage,omitempty"`
	TotalSavings         float64                      `gorm:"-" json:"total_savings"`
	UniqueUsers          int                          `gorm:"-" json:"unique_users"`
}

type VoucherProductRestriction struct {
	ID         uint64    `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	CreatedAt  time.Time `gorm:"column:created_at" json:"created_at"`
	VoucherID  uint64    `gorm:"not null;column:voucher_id" json:"voucher_id"`
	ProductID  *uint64   `gorm:"column:product_id" json:"product_id,omitempty"`
	CategoryID *uint64   `gorm:"column:category_id" json:"category_id,omitempty"`
	IsIncluded bool      `gorm:"not null;column:is_included" json:"is_included"`

	ProductName  *string `gorm:"-" json:"product_name,omitempty"`
	CategoryName *string `gorm:"-" json:"category_name,omitempty"`
}

type VoucherTimeRestriction struct {
	ID                   uint64              `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	CreatedAt            time.Time           `gorm:"column:created_at" json:"created_at"`
	VoucherID            uint64              `gorm:"not null;column:voucher_id" json:"voucher_id"`
	RestrictionType      TimeRestrictionType `gorm:"type:varchar(50);not null;column:restriction_type" json:"restriction_type"`
	AllowedDaysOfWeek    Int32Array          `gorm:"type:integer[];column:allowed_days_of_week" json:"allowed_days_of_week"`
	AllowedHoursStart    *int32              `gorm:"column:allowed_hours_start" json:"allowed_hours_start,omitempty"`
	AllowedHoursEnd      *int32              `gorm:"column:allowed_hours_end" json:"allowed_hours_end,omitempty"`
	SpecificDates        TimeArray           `gorm:"column:specific_dates" json:"specific_dates"`
	RecurrencePattern    *RecurrencePattern  `gorm:"type:varchar(50);column:recurrence_pattern" json:"recurrence_pattern,omitempty"`
	RecurrenceDayOfMonth *int32              `gorm:"column:recurrence_day_of_month" json:"recurrence_day_of_month,omitempty"`
	RecurrenceMonth      *int32              `gorm:"column:recurrence_month" json:"recurrence_month,omitempty"`
	RecurrenceDayOfWeek  *int32              `gorm:"column:recurrence_day_of_week" json:"recurrence_day_of_week,omitempty"`
}

type VoucherUserEligibility struct {
	ID                uint64    `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	CreatedAt         time.Time `gorm:"column:created_at" json:"created_at"`
	VoucherID         uint64    `gorm:"not null;column:voucher_id" json:"voucher_id"`
	UserID            *uint64   `gorm:"column:user_id" json:"user_id,omitempty"`
	UserType          *string   `gorm:"type:varchar(50);column:user_type" json:"user_type,omitempty"`
	MinAccountAgeDays *int32    `gorm:"column:min_account_age_days" json:"min_account_age_days,omitempty"`
	MaxAccountAgeDays *int32    `gorm:"column:max_account_age_days" json:"max_account_age_days,omitempty"`
	MinPreviousOrders *uint64   `gorm:"column:min_previous_orders" json:"min_previous_orders,omitempty"`
	MaxPreviousOrders *uint64   `gorm:"column:max_previous_orders" json:"max_previous_orders,omitempty"`
}

type VoucherOrderUsage struct {
	OrderID     string    `json:"order_id"`
	UsedAt      time.Time `json:"used_at"`
	OrderAmount float64   `json:"order_amount"`
	Status      string    `json:"status"`
}

type UserVoucherUsage struct {
	UserID     uint64               `json:"user_id"`
	UsageCount int                  `json:"usage_count"`
	FullName   string               `json:"full_name"`
	Email      string               `json:"email"`
	Type       string               `json:"type"`
	Orders     []*VoucherOrderUsage `json:"orders,omitempty"`
}

type CartItem struct {
	ProductID  *uint64 `json:"product_id,omitempty"`
	CategoryID *uint64 `json:"category_id,omitempty"`
	Quantity   int     `json:"quantity"`
	Price      float64 `json:"price"`
}

type CreateVoucherRequest struct {
	VoucherCode       string      `json:"voucher_code" validate:"required,min=3,max=50"`
	Title             string      `json:"title" validate:"required,min=1,max=255"`
	Description       string      `json:"description"`
	DiscountTypeID    uint64      `json:"discount_type_id" validate:"required"`
	DiscountValue     float64     `json:"discount_value" validate:"required,gt=0"`
	UsageMethod       UsageMethod `json:"usage_method" validate:"required"`
	ValidFrom         time.Time   `json:"valid_from" validate:"required"`
	ValidUntil        time.Time   `json:"valid_until" validate:"required"`
	MaxUsageCount     *int        `json:"max_usage_count"`
	MaxUsagePerUser   *int        `json:"max_usage_per_user"`
	MinOrderAmount    float64     `json:"min_order_amount" validate:"gte=0"`
	MaxDiscountAmount *float64    `json:"max_discount_amount"`
}

type UpdateVoucherRequest struct {
	Title             string      `json:"title" validate:"required,min=1,max=255"`
	Description       string      `json:"description"`
	DiscountTypeID    uint64      `json:"discount_type_id" validate:"required"`
	DiscountValue     float64     `json:"discount_value" validate:"required,gt=0"`
	UsageMethod       UsageMethod `json:"usage_method" validate:"required"`
	Status            string      `json:"status" validate:"required"`
	MinOrderAmount    float64     `json:"min_order_amount" validate:"gte=0"`
	MaxDiscountAmount *float64    `json:"max_discount_amount"`
	MaxUsageCount     *int        `json:"max_usage_count"`
	MaxUsagePerUser   *int        `json:"max_usage_per_user"`
	ValidFrom         time.Time   `json:"valid_from" validate:"required"`
	ValidUntil        time.Time   `json:"valid_until" validate:"required"`

	ProductRestrictions []*VoucherProductRestriction `json:"product_restrictions"`
	TimeRestrictions    []*VoucherTimeRestriction    `json:"time_restrictions"`
	UserEligibility     []*VoucherUserEligibility    `json:"user_eligibility"`
}

type ListVouchersRequest struct {
	Page           int          `json:"page" query:"page"`
	Limit          int          `json:"limit" query:"limit"`
	Search         string       `json:"search" query:"search"`
	DiscountTypeID *string      `json:"discount_type_id" query:"discount_type_id"`
	UsageMethod    *UsageMethod `json:"usage_method" query:"usage_method"`
	Status         string       `json:"status" query:"status"`
	SortBy         string       `json:"sort_by" query:"sort_by"`
	SortOrder      string       `json:"sort_order" query:"sort_order"`
}

type VoucherListItem struct {
	ID               uint64  `json:"id"`
	VoucherCode      string  `json:"voucher_code"`
	Title            string  `json:"title"`
	DiscountTypeName string  `json:"discount_type_name"`
	DiscountTypeCode string  `json:"discount_type_code"`
	DiscountValue    float64 `json:"discount_value"`
	UsageMethod      string  `json:"usage_method"`
	ValidFrom        string  `json:"valid_from"`
	ValidUntil       string  `json:"valid_until"`
	CreatedBy        uint64  `json:"created_by"`
	Status           string  `json:"status"`
	CreatedAt        string  `json:"created_at"`
	UpdatedAt        string  `json:"updated_at"`
}

type PaginatedResponse[T any] struct {
	Data  []T `json:"data"`
	Total int `json:"total"`
	Page  int `json:"page"`
	Limit int `json:"limit"`
}

type VoucherEligibilityRequest struct {
	VoucherCode    string     `json:"voucher_code" validate:"required"`
	UserID         uint64     `json:"user_id" validate:"required"`
	OrderAmount    float64    `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp time.Time  `json:"order_timestamp"`
	CartItems      []CartItem `json:"cart_items"`
}

type VoucherEligibilityResponse struct {
	Eligible         bool    `json:"eligible"`
	Message          string  `json:"message"`
	VoucherID        uint64  `json:"voucher_id"`
	DiscountAmount   float64 `json:"discount_amount"`
	DiscountTypeName string  `json:"discount_type_name"`
	DiscountTypeCode string  `json:"discount_type_code"`
	VoucherCode      string  `json:"voucher_code"`
	Title            string  `json:"title"`
	Description      string  `json:"description"`
	Status           *string `json:"status,omitempty"`
}

type AutoVoucherEligibilityRequest struct {
	UserID         uint64     `json:"user_id" validate:"required"`
	OrderAmount    float64    `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp time.Time  `json:"order_timestamp"`
	CartItems      []CartItem `json:"cart_items"`
}

func (Voucher) TableName() string {
	return "vouchers"
}

func (DiscountType) TableName() string {
	return "discount_types"
}

func (VoucherProductRestriction) TableName() string {
	return "voucher_product_restrictions"
}

func (VoucherTimeRestriction) TableName() string {
	return "voucher_time_restrictions"
}

func (VoucherUserEligibility) TableName() string {
	return "voucher_user_eligibility"
}

func GetAllModels() []any {
	return []any{
		&Voucher{},
		&DiscountType{},
		&VoucherProductRestriction{},
		&VoucherTimeRestriction{},
		&VoucherUserEligibility{},
	}
}
